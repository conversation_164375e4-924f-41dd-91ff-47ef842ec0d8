import os
import logging
import requests
import streamlit as st
from typing import List, Dict, Optional
import json
from prompts import templates

# -------------------------------
# Logging Setup
# -------------------------------
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s]: %(message)s"
)
logger = logging.getLogger(__name__)


# -------------------------------
# LidarService Class
# -------------------------------
import os
import requests
import logging
from typing import Optional

logger = logging.getLogger(__name__)

class LidarService:
    """Handles communication with the Lidar LLM inference service."""

    def __init__(self, base_url: Optional[str] = None):
        self.base_url = base_url or os.environ.get("LIDAR_INFERENCE_SERVICE_URL")
        if not self.base_url:
            logger.warning("Environment variable LIDAR_INFERENCE_SERVICE_URL not set.")
        self.stream_endpoint = f"{self.base_url}/api/v1/generate-stream-with-context"
        self.context_endpoint = f"{self.base_url}/api/v1/get_lidar"
    
    def format_context_for_llm(self,context_json: dict) -> str:
        timestamp = context_json.get("timestamp", "N/A")
        context_info = context_json.get("context", "")

        formatted = (
            f"📅 Timestamp: {timestamp}\n\n"
            f"📝 Lidar Context:\n"
            f"{context_info.strip()}\n\n"
        )
        return formatted

    def _fetch_context(self) -> str:
        """Fetches Lidar context from the inference service."""
        try:
            response = requests.get(self.context_endpoint, timeout=15)
            response.raise_for_status()
            context_json = response.json()
            #context_str = json.dumps(context_json, indent=2)
            context_str = self.format_context_for_llm(context_json)
            return context_str
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch context: {e}")
            raise RuntimeError(f"Error fetching context from {self.context_endpoint}: {e}")

    def generate_stream(self, prompt: str, timeout: int = 60) -> str:
        """
        Sends prompt + context to the inference server via POST request
        and streams back the model’s response.
        """
        if not self.base_url:
            raise RuntimeError("LIDAR_INFERENCE_SERVICE_URL is not configured.")

        context = self._fetch_context()
        payload = {"prompt": prompt, "context": context}
        response_text = ""

        try:
            with requests.post(self.stream_endpoint, json=payload, stream=True, timeout=timeout) as response:
                response.raise_for_status()

                logger.info(f"Streaming response from {self.stream_endpoint} for prompt: {prompt[:50]}...")
                for chunk in response.iter_content(chunk_size=1024):
                    if chunk:
                        decoded_chunk = chunk.decode("utf-8", errors="ignore")
                        response_text += decoded_chunk
                return response_text

        except requests.exceptions.Timeout:
            logger.error("Request to Lidar inference service timed out.")
            raise RuntimeError("The request to Lidar inference service timed out.")
        except requests.exceptions.RequestException as e:
            logger.error(f"Error during inference request: {e}")
            raise RuntimeError(f"Failed to connect to Lidar inference service: {e}")

# -------------------------------
# LidarChatApp Class
# -------------------------------
class LidarChatApp:
    """Streamlit application for interacting with Lidar LLM."""

    def __init__(self):
        self.available_models = ["llama3:8b","gemma3:4b"]
        self.available_prompts = [templates.PROMPT_TEMPLATE]
        self.service = LidarService()
        self._init_session_state()

    def _init_session_state(self):
        """Initializes Streamlit session state."""
        if "messages" not in st.session_state:
            st.session_state.messages: List[Dict[str, str]] = []

    def _render_sidebar(self):
        """Renders sidebar components and controls."""
        with st.sidebar:
            st.image("images/TU_Berlin.png", width="stretch")

            # ---------------------------
            # New: Model selection
            # ---------------------------
            st.subheader("🤖 Select Model")
            selected_model = st.selectbox("Choose Lidar Model:", self.available_models)
            # st.session_state.selected_model = selected_model
            
            # ---------------------------
            # New: Prompt selection
            # ---------------------------
            st.subheader("💬 Choose Prompt")
            selected_prompt = st.selectbox("Pick a Prompt:", self.available_prompts)
            #st.session_state.selected_prompt = selected_promp

            st.header("⚙️ Controls")

            if st.button("🗑️ Clear Chat"):
                st.session_state.messages.clear()
                st.rerun()

            st.subheader("🌍 Environment")
            st.markdown(
                f"""
                <div><b>LIDAR_INFERENCE_SERVICE_URL:</b> 
                <span>{self.service.base_url or 'Not Set'}</span></div>
                """,
                unsafe_allow_html=True,
            )

            st.divider()

            st.markdown(
                """
                **📄 About**
                
                This demo showcases a conversational interface with the **Lidar LLM service**, 
                developed for research and educational purposes.
                
                © 2025 **TU Berlin**  
                All rights reserved.
                """
            )

    def _display_chat_history(self):
        """Displays the chat history from session state."""
        for msg in st.session_state.messages:
            with st.chat_message(msg["role"]):
                st.markdown(msg["content"])

    def _handle_user_input(self, prompt: str):
        """Handles user input and generates assistant response."""
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user", avatar="👤"):
            st.markdown(prompt)

        with st.chat_message("assistant", avatar="📡"):
            response_placeholder = st.empty()
            response_text = ""

            with st.spinner("🚗 *Analyzing surroundings...*"):
                try:
                    response_text = self.service.generate_stream(prompt)
                    response_placeholder.markdown(response_text)
                except Exception as e:
                    error_msg = f"❌ Error: {str(e)}"
                    st.error(error_msg)
                    response_text = error_msg

        st.session_state.messages.append({"role": "assistant", "content": response_text})

    def run(self):
        """Runs the Streamlit app."""
        st.set_page_config(page_title="TU Berlin - Lidar LLM", layout="wide")
        st.title("🚗  LidarLLM")
        st.markdown("Chat with the Lidar LLM — send prompts and get streamed responses in real time.")

        self._render_sidebar()
        self._display_chat_history()

        if prompt := st.chat_input("Ask something about Lidar data, driving scenes, or detections..."):
            self._handle_user_input(prompt)


# -------------------------------
# Main Entry Point
# -------------------------------
if __name__ == "__main__":
    try:
        app = LidarChatApp()
        app.run()
    except Exception as e:
        st.error(f"🚨 Critical Error: {e}")
        logger.exception("Application crashed.")
