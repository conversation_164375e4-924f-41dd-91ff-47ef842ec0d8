# Lidar LLM

### Author: <PERSON><PERSON>

**LidarLLM** is a modular system for Lidar data processing and inference. It consists of:

* A GPU-powered **Ollama LLM backend** for AI inference
* A **FastAPI backend** for Lidar data processing and API endpoints
* A **Streamlit frontend** for visualization

---

![LidarLLM App](assets/images/lidar_llm_app.png)


## Services Overview

### 1. **Ollama Service (GPU)**

* Container Name: `ollama-gpu`
* Role: Hosts Ollama LLM models and provides GPU-accelerated inference
* Port: `11434`
* GPU-enabled via Docker device reservations
* Model storage: `ollama_models` Docker volume
* Startup script: `OllamaService/preload-model.sh` (preload required models)

---

### 2. **Lidar Inference Service (FastAPI Backend)**

* Container Name: `lidar-inference-service`
* Role: Provides API endpoints to interact with Lidar data and Ollama LLM
* Port: `8000`
* Depends on: Ollama service
* Environment variable: `OLLAMA_API_URL=http://ollama:11434`
* Source code: `LidarInferenceService/app`
* Startup script: `LidarInferenceService/start.sh`

---

### 3. **UI Service (Streamlit Frontend)**

* Container Name: `ui-service`
* Role: Visualizes inference results and provides a user interface
* Port: `8501`
* Depends on: Backend service
* Environment variable: `LIDAR_INFERENCE_SERVICE_URL=http://lidar-inference-service:8000`
* Source code: `UIService/app.py`

---

### 4. **Lidar Metadata Extraction Service**

* Container Name: `lidar-metadata-service`
* Role: Processes Lidar `.pcd` files and converts them to `.bin` or extracts point cloud metadata
* Source code: `LidarMetadataExtractionService/`

---

## Getting Started

### Prerequisites

* Docker & Docker Compose installed
* NVIDIA GPU drivers (if using Ollama GPU service)
* Linux/macOS recommended

---

### 1. Build and Start All Services

```bash
./start_containers.sh
```

This will start all services:

* Ollama GPU service
* FastAPI backend
* Streamlit frontend
* Metadata extraction service (if included in `docker-compose.yml`)

---

### 2. Stop All Services

```bash
./stop_containers.sh
```

---

### 3. Access Services

* **Frontend UI:** [http://localhost:8501](http://localhost:8501)
* **Backend API:** [http://localhost:8000](http://localhost:8000)
* **Ollama API:** Accessible internally via `http://ollama:11434`

---

### 4. Docker Volumes

* `ollama_models`: Stores LLM models for Ollama service
* `lidar_db_data`: Persistent storage for backend data (if configured)

---

### 5. Backend Health Check

```bash
http://localhost:8000/health
```

---

### 6. Development Tips

* Backend supports live reload: changes in `LidarInferenceService/app` automatically refresh the server
* Frontend reloads on save (`--server.runOnSave=true`)
* Tests for backend API are located in `LidarInferenceService/app/tests/api/v1/`

---

### 7. Notes

* Ensure Ollama GPU models are preloaded using `OllamaService/preload-model.sh`
* Backend communicates with Ollama service using Docker service name `ollama` for internal requests
* Lidar data processing is modular, with separate services for inference and metadata extraction
