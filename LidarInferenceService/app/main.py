from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.api.v1.llm import router as LLM_router
from app.api.v1.lidar import router as Lidar_router
from app.core.config import config
from app.db.schema import Base, engine

# Create database tables
# Base.metadata.create_all(bind=engine)


DESRIPTION = """
**Lidar Inference Service**
"""

app = FastAPI(
    title=config.app_name,
    description=config.app_description,
    version=config.app_version,
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # React dev server and Docker
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(LLM_router, prefix="/api/v1", tags=["llm"])
app.include_router(Lidar_router, prefix="/api/v1", tags=["lidar"])

@app.get("/")
async def root():
    return {
        "message": "Welcome to Lidar Inference Service!",
        "docs_url": "http://localhost:8000/docs"
        }

@app.get("/health")
async def health_check():
    return {"status": "healthy", "app_name": config.app_name}