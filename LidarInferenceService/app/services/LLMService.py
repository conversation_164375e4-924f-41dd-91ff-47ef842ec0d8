import re
from datetime import datetime
from typing import List, Optional
import subprocess
from sqlalchemy.orm import Session
from app.models.llm import RequestModel
from app.prompts.llm import PROMPT_TEMPLATE
import requests
import os
from langchain_core.prompts import ChatPromptTemplate
from langchain_ollama.llms import OllamaLLM


class LLMService:
    def __init__(self):
        MODEL= "llama3:8b"
        base_url = os.environ.get("OLLAMA_BASE_URL", "http://ollama:11434")
        self.model = OllamaLLM(model=MODEL, base_url=base_url)

    def generate_llm_response(self,prompt: str, context: str) -> str:
        result = self.call_generate_llm_response(input_text=prompt, context=context)
        return f"LLM response for: {result}"

    def call_generate_llm_response(self,input_text, context):
        try:
            prompt = ChatPromptTemplate.from_template(PROMPT_TEMPLATE)
            chain = prompt | self.model
            response = chain.invoke({"input": input_text, "context": context})
            return response
        
        except Exception as e:
            print(f"Error generating LLM response: {e}")
            return f"Error generating LLM response: {e}"
    
    def stream_llm_response(self, prompt: str, context: str):
        """
        Yields chunks of LLM response as they are generated.
        """
        try:
            prompt_template = ChatPromptTemplate.from_template(PROMPT_TEMPLATE)
            chain = prompt_template | self.model
            
            # Assume `chain.invoke_stream` is a streaming API
            for chunk in chain.stream({"input": prompt, "context": context}):
                yield chunk

        except Exception as e:
            yield f"Error generating LLM response: {e}"
            