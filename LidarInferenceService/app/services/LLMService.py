import re
from datetime import datetime
from typing import List, Optional
import subprocess
from sqlalchemy.orm import Session
from app.models.llm import <PERSON>questModel
from app.prompts.llm import PROMPT_TEMPLATE, AVAILABLE_PROMPTS
import requests
import os
from langchain_core.prompts import <PERSON>t<PERSON><PERSON>pt<PERSON>emplate
from langchain_ollama.llms import OllamaLLM


class LLMService:
    def __init__(self):
        self.base_url = os.environ.get("OLLAMA_BASE_URL", "http://ollama:11434")
        self.default_model = "llama3:8b"
        # Cache for model instances to avoid recreating them
        self.model_cache = {}

    def get_model(self, model_name: str = None):
        """Get or create a model instance for the specified model name."""
        if model_name is None:
            model_name = self.default_model

        if model_name not in self.model_cache:
            self.model_cache[model_name] = OllamaLLM(model=model_name, base_url=self.base_url)

        return self.model_cache[model_name]

    def get_prompt_template(self, system_prompt: str = None):
        """Get the appropriate prompt template."""
        if system_prompt is None:
            return PROMPT_TEMPLATE

        # Check if it's a predefined prompt name
        if system_prompt in AVAILABLE_PROMPTS:
            return AVAILABLE_PROMPTS[system_prompt]

        # Otherwise, treat it as a custom prompt template
        # Ensure it has the required placeholders
        if "{input}" not in system_prompt or "{context}" not in system_prompt:
            # Add placeholders if missing
            system_prompt += "\n\nUser Input: {input}\nLiDAR Context: {context}"

        return system_prompt

    def generate_llm_response(self, prompt: str, context: str, model_name: str = None, system_prompt: str = None) -> str:
        result = self.call_generate_llm_response(input_text=prompt, context=context, model_name=model_name, system_prompt=system_prompt)
        return f"LLM response for: {result}"

    def call_generate_llm_response(self, input_text, context, model_name: str = None, system_prompt: str = None):
        try:
            model = self.get_model(model_name)
            prompt_template = self.get_prompt_template(system_prompt)
            prompt = ChatPromptTemplate.from_template(prompt_template)
            chain = prompt | model
            response = chain.invoke({"input": input_text, "context": context})
            return response

        except Exception as e:
            print(f"Error generating LLM response: {e}")
            return f"Error generating LLM response: {e}"
    
    def stream_llm_response(self, prompt: str, context: str, model_name: str = None, system_prompt: str = None):
        """
        Yields chunks of LLM response as they are generated.
        """
        try:
            model = self.get_model(model_name)
            prompt_template = self.get_prompt_template(system_prompt)
            prompt_template_obj = ChatPromptTemplate.from_template(prompt_template)
            chain = prompt_template_obj | model

            # Stream the response
            for chunk in chain.stream({"input": prompt, "context": context}):
                yield chunk

        except Exception as e:
            yield f"Error generating LLM response: {e}"
            