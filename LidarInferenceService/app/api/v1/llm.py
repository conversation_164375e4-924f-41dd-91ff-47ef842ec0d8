from typing import List
import json
from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import StreamingResponse
from app.db.schema import SessionLocal
from app.models.llm import RequestModel, ResponseModel
from app.services.LLMService import LLMService
from typing import Optional
from app.data.LidarData import latest_lidar_data, data_lock

router = APIRouter()

# ------------------------
# Endpoint using schemas
# ------------------------

# def get_relevant_context():
#     if not latest_lidar_data:
#         return {"error": "No data available"}
#     print("Total Frames of Data Available: ", len(latest_lidar_data))
#     return latest_lidar_data[0]  # most recent

def get_relevant_context():
    if not latest_lidar_data:
        return {"error": "No data available"}
    print("Total Frames of Data Available: ", len(latest_lidar_data))
    return latest_lidar_data[0]  # most recent


llm = LLMService()

@router.get("/generate", response_model=ResponseModel)
def generate(prompt: str = Query(...), context: Optional[str] = Query(None)):
    
    try:
        context = get_relevant_context()
        response_text = llm.generate_llm_response(prompt, context)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
    return ResponseModel(response=response_text)


@router.post("/generate", response_model=ResponseModel)
def generate(request: RequestModel):
    try:
        context = get_relevant_context()
        response_text = llm.generate_llm_response(request.prompt, context)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
    return ResponseModel(response=response_text)


@router.get("/generate-stream")
def generate_stream(prompt: str):
    context = get_relevant_context()
    # Generator that yields context first, then LLM stream
    def generate():
        # Send context first
        yield f"**📋 Context:**\n```\n{context}\n```\n\n**🤖 Response:**\n\n"
        # Then stream LLM response
        yield from llm.stream_llm_response(prompt, context)
    return StreamingResponse(generate(), media_type="text/markdown")


@router.post("/generate-stream-with-context")
async def generate_stream(request: RequestModel):
    """Stream LLM response along with context."""
    prompt = request.prompt
    context = request.context

    def generate():
        yield f"**📋 Context:**\n```\n{context}\n```\n\n**🤖 Response:**\n\n"
        yield from llm.stream_llm_response(prompt, context)

    return StreamingResponse(generate(), media_type="text/markdown")