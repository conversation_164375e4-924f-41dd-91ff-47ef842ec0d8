from pydantic import BaseModel
from typing import List
import time
import threading
import json
from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import StreamingResponse
from app.db.schema import SessionLocal
from app.models.lidar import LidarMetadata
from app.data.LidarData import latest_lidar_data, data_lock
from typing import Optional

router = APIRouter()

# ------------------------
# Endpoint to receive Lidar data
# ------------------------
@router.post("/update_lidar")
async def update_lidar(data: LidarMetadata):
    global latest_lidar_data, data_timestamp
    with data_lock:
        # Add newest entry at the front
        latest_lidar_data.insert(0, data.dict())
        # Keep only last 10 entries
        latest_lidar_data = latest_lidar_data[:10]
        data_timestamp = time.time()
    return {"status": "updated", "stored_count": len(latest_lidar_data)}

# ------------------------
# Endpoint to get most recent Lidar data
# ------------------------
@router.get("/get_lidar")
async def get_lidar():
    global latest_lidar_data
    with data_lock:
        if not latest_lidar_data:
            return {"error": "No data available"}
        return latest_lidar_data[0]  # most recent

# ------------------------
# Endpoint to get up to last 10 LiDAR metadata entries
# ------------------------
@router.get("/get_lidar_history")
async def get_lidar_history(limit: int = 10):
    global latest_lidar_data
    with data_lock:
        if not latest_lidar_data:
            return {"error": "No data available"}
        return latest_lidar_data[:limit]


def clean_old_data(max_age_sec=30):
    global latest_lidar_data, data_timestamp
    while True:
        time.sleep(1)
        with data_lock:
            if latest_lidar_data:
                age = time.time() - data_timestamp
                # Only trim if data is older than max_age_sec and there are more than 15 objects
                if age > max_age_sec and len(latest_lidar_data) > 15:
                    # Keep only the 15 most recent objects
                    latest_lidar_data = latest_lidar_data[:15]

# Start background cleanup thread
threading.Thread(target=clean_old_data, daemon=True).start()

