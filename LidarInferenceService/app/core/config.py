from dotenv import load_dotenv
from pydantic_settings import BaseSettings

load_dotenv()

DESCRIPTION = """
This FastAPI service provides endpoints to generate responses using a language model (`LLMService`). The service supports both standard and streaming responses, with optional context input.

| Method | Endpoint           | Request Type / Params                                                    | Response Model                      | Description                                                                                     |
| ------ | ------------------ | ------------------------------------------------------------------------ | ----------------------------------- | ----------------------------------------------------------------------------------------------- |
| GET    | `/generate`        | Query: `prompt` (str, required), `context` (str, optional)               | `ResponseModel`                     | Generates a response for the given prompt and optional context.                                 |
| POST   | `/generate`        | JSON Body: `{ "prompt": str, "context": str (optional) }`                | `ResponseModel`                     | Generates a response from JSON input containing prompt and optional context.                    |
| GET    | `/generate-stream` | Query: `prompt` (str, required), `context` (str, optional, default=`""`) | StreamingResponse (`text/markdown`) | Streams the generated response in real-time. Useful for chat interfaces or incremental updates. |
"""

class Config(BaseSettings):
    app_name: str = "Lidar Inference API Service"
    app_description: str = DESCRIPTION
    app_version: str = "1.0.0"
    debug: bool = False
    db_user: str = ""
    db_password: str = ""
    db_name: str = "test.db"

    @property
    def db_url(self):
        return f"sqlite:///./{self.db_name}"


config = Config()