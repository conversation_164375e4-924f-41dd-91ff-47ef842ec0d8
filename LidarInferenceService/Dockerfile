# Use Hugging Face PyTorch GPU image as base
FROM huggingface/transformers-pytorch-gpu:latest

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY ./requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy FastAPI app and startup script
COPY ./app ./app
COPY ./start.sh /app/start.sh

# Make startup script executable
RUN chmod +x /app/start.sh

# Expose FastAPI port
EXPOSE 8000

# Run startup script
CMD ["/app/start.sh"]
