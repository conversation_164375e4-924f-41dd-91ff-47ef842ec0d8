mmdet3d.apis
--------------
.. automodule:: mmdet3d.apis
    :members:

mmdet3d.datasets
--------------

datasets
^^^^^^^^^^
.. automodule:: mmdet3d.datasets
    :members:

transforms
^^^^^^^^^^^^
.. automodule:: mmdet3d.datasets.transforms
    :members:

mmdet3d.engine
--------------

hooks
^^^^^^^^^^
.. automodule:: mmdet3d.engine.hooks
    :members:

mmdet3d.evaluation
--------------------

functional
^^^^^^^^^^^^^^^^^
.. automodule:: mmdet3d.evaluation.functional
    :members:

metrics
^^^^^^^^^^
.. automodule:: mmdet3d.evaluation.metrics
    :members:

mmdet3d.models
--------------

backbones
^^^^^^^^^^^^^^^^^^
.. automodule:: mmdet3d.models.backbones
    :members:

data_preprocessors
^^^^^^^^^^^^^^^^^^^^^^^^^^
.. automodule:: mmdet3d.models.data_preprocessors
    :members:

decode_heads
^^^^^^^^^^^^^^^
.. automodule:: mmdet3d.models.decode_heads
    :members:

dense_heads
^^^^^^^^^^^^^^^
.. automodule:: mmdet3d.models.dense_heads
    :members:

detectors
^^^^^^^^^^
.. automodule:: mmdet3d.models.detectors
    :members:

layers
^^^^^^^^^^
.. automodule:: mmdet3d.models.layers
    :members:

losses
^^^^^^^^^^
.. automodule:: mmdet3d.models.losses
    :members:

middle_encoders
^^^^^^^^^^^^
.. automodule:: mmdet3d.models.middle_encoders
    :members:

necks
^^^^^^^^^^^^
.. automodule:: mmdet3d.models.necks
    :members:

roi_heads
^^^^^^^^^^^^^
.. automodule:: mmdet3d.models.roi_heads
    :members:

segmentors
^^^^^^^^^^^^^
.. automodule:: mmdet3d.models.segmentors
    :members:

task_modules
^^^^^^^^^^^^^
.. automodule:: mmdet3d.models.task_modules
    :members:

test_time_augs
^^^^^^^^^^^^^^^^^^^^
.. automodule:: mmdet3d.models.test_time_augs
    :members:

utils
^^^^^^^^^^
.. automodule:: mmdet3d.models.utils
    :members:

voxel_encoders
^^^^^^^^^^^^^
.. automodule:: mmdet3d.models.voxel_encoders
    :members:

mmdet3d.structures
--------------------

structures
^^^^^^^^^^^^^^^^^
.. automodule:: mmdet3d.structures
    :members:

bbox_3d
^^^^^^^^^^
.. automodule:: mmdet3d.structures.bbox_3d
    :members:

ops
^^^^^^^^^^
.. automodule:: mmdet3d.structures.ops
    :members:

points
^^^^^^^^^^
.. automodule:: mmdet3d.structures.points
    :members:

mmdet3d.testing
----------------
.. automodule:: mmdet3d.testing
    :members:

mmdet3d.visualization
--------------------
.. automodule:: mmdet3d.visualization
    :members:

mmdet3d.utils
--------------
.. automodule:: mmdet3d.utils
    :members:
