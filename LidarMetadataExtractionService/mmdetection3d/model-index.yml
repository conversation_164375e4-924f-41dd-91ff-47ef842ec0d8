Import:
  - configs/3dssd/metafile.yml
  - configs/centerpoint/metafile.yml
  - configs/dgcnn/metafile.yml
  - configs/dynamic_voxelization/metafile.yml
  - configs/fcos3d/metafile.yml
  - configs/free_anchor/metafile.yml
  - configs/groupfree3d/metafile.yml
  - configs/h3dnet/metafile.yml
  - configs/imvotenet/metafile.yml
  - configs/imvoxelnet/metafile.yml
  - configs/monoflex/metafile.yml
  - configs/mvxnet/metafile.yml
  - configs/nuimages/metafile.yml
  - configs/paconv/metafile.yml
  - configs/parta2/metafile.yml
  - configs/pgd/metafile.yml
  - configs/point_rcnn/metafile.yml
  - configs/pointnet2/metafile.yml
  - configs/pointpillars/metafile.yml
  - configs/regnet/metafile.yml
  - configs/second/metafile.yml
  - configs/smoke/metafile.yml
  - configs/ssn/metafile.yml
  - configs/votenet/metafile.yml
  - configs/minkunet/metafile.yml
  - configs/cylinder3d/metafile.yml
  - configs/pv_rcnn/metafile.yml
  - configs/fcaf3d/metafile.yml
  - configs/spvcnn/metafile.yml
