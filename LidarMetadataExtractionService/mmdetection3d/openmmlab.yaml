name: openmmlab
channels:
  - defaults
  - nvidia
  - pytorch
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - backcall=0.2.0=pyhd3eb1b0_0
  - blas=1.0=mkl
  - brotli-python=1.0.9=py38h6a678d5_8
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2025.7.15=h06a4308_0
  - certifi=2024.8.30=py38h06a4308_0
  - charset-normalizer=3.3.2=pyhd3eb1b0_0
  - cuda-cudart=11.7.99=0
  - cuda-cupti=11.7.101=0
  - cuda-libraries=11.7.1=0
  - cuda-nvrtc=11.7.99=0
  - cuda-nvtx=11.7.91=0
  - cuda-runtime=11.7.1=0
  - cuda-version=12.9=3
  - debugpy=1.6.7=py38h6a678d5_0
  - ffmpeg=4.3=hf484d3e_0
  - freetype=2.13.3=h4a9f257_0
  - gmp=6.3.0=h6a678d5_0
  - gmpy2=2.1.2=py38heeb90bb_0
  - gnutls=3.6.15=he1e5248_0
  - idna=3.7=py38h06a4308_0
  - importlib_metadata=7.0.1=hd3eb1b0_0
  - intel-openmp=2023.1.0=hdb19cb5_46306
  - ipykernel=6.29.5=py38h06a4308_0
  - jinja2=3.1.4=py38h06a4308_0
  - jpeg=9e=h5eee18b_3
  - jupyter_client=8.6.0=py38h06a4308_0
  - jupyter_core=5.7.2=py38h06a4308_0
  - lame=3.100=h7b6447c_0
  - lcms2=2.16=hb9589c4_0
  - ld_impl_linux-64=2.40=h12ee557_0
  - lerc=4.0.0=h6a678d5_0
  - libcublas=**********=0
  - libcufft=**********=h4fbf590_0
  - libcufile=********=4
  - libcurand=**********=0
  - libcusolver=********=0
  - libcusparse=*********=0
  - libdeflate=1.22=h5eee18b_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libiconv=1.16=h5eee18b_3
  - libidn2=2.3.4=h5eee18b_0
  - libnpp=*********=0
  - libnvjpeg=********=0
  - libpng=1.6.39=h5eee18b_0
  - libsodium=1.0.18=h7b6447c_0
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtasn1=4.19.0=h5eee18b_0
  - libtiff=4.5.1=hffd6297_1
  - libunistring=0.9.10=h27cfd23_0
  - libwebp-base=1.3.2=h5eee18b_1
  - libxcb=1.17.0=h9b100fa_0
  - lz4-c=1.9.4=h6a678d5_1
  - markupsafe=2.1.3=py38h5eee18b_0
  - mkl=2023.1.0=h213fc3f_46344
  - mkl-service=2.4.0=py38h5eee18b_1
  - mkl_fft=1.3.8=py38h5eee18b_0
  - mkl_random=1.2.4=py38hdb19cb5_0
  - mpc=1.3.1=h5eee18b_0
  - mpfr=4.2.1=h5eee18b_0
  - mpmath=1.3.0=py38h06a4308_0
  - ncurses=6.5=h7934f7d_0
  - nest-asyncio=1.6.0=py38h06a4308_0
  - nettle=3.7.3=hbbd107a_1
  - networkx=3.1=py38h06a4308_0
  - numpy=1.24.3=py38hf6e8229_1
  - numpy-base=1.24.3=py38h060ed82_1
  - openh264=2.1.1=h4ff587b_0
  - openjpeg=2.5.2=he7f1fd0_0
  - openssl=3.0.17=h5eee18b_0
  - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pillow=10.4.0=py38h5eee18b_0
  - pip=24.2=py38h06a4308_0
  - psutil=5.9.0=py38h5eee18b_0
  - pthread-stubs=0.3=h0ce48e5_1
  - ptyprocess=0.7.0=pyhd3eb1b0_2
  - pure_eval=0.2.2=pyhd3eb1b0_0
  - pysocks=1.7.1=py38h06a4308_0
  - python=3.8.20=he870216_0
  - python-dateutil=2.9.0post0=py38h06a4308_2
  - pytorch=2.0.1=py3.8_cuda11.7_cudnn8.5.0_0
  - pytorch-cuda=11.7=h778d358_5
  - pytorch-mutex=1.0=cuda
  - pyzmq=25.1.2=py38h6a678d5_0
  - readline=8.3=hc2a1206_0
  - sqlite=3.50.2=hb25bd0a_1
  - stack_data=0.2.0=pyhd3eb1b0_0
  - sympy=1.13.3=py38h06a4308_0
  - tbb=2021.8.0=hdb19cb5_0
  - tk=8.6.15=h54e0aa7_0
  - torchaudio=2.0.2=py38_cu117
  - torchtriton=2.0.0=py38
  - torchvision=0.15.2=py38_cu117
  - tornado=6.4.1=py38h5eee18b_0
  - traitlets=5.14.3=py38h06a4308_0
  - wheel=0.44.0=py38h06a4308_0
  - xorg-libx11=1.8.12=h9b100fa_1
  - xorg-libxau=1.0.12=h9b100fa_0
  - xorg-libxdmcp=1.1.5=h9b100fa_0
  - xorg-xorgproto=2024.1=h5eee18b_1
  - xz=5.6.4=h5eee18b_1
  - zeromq=4.3.5=h6a678d5_0
  - zipp=3.20.2=py38h06a4308_0
  - zlib=1.2.13=h5eee18b_1
  - zstd=1.5.6=hc292b87_0
  - pip:
      - absl-py==2.3.1
      - addict==2.4.0
      - aliyun-python-sdk-core==2.16.0
      - aliyun-python-sdk-kms==2.16.5
      - annotated-types==0.7.0
      - anyio==4.5.2
      - asttokens==3.0.0
      - attrs==25.3.0
      - black==24.8.0
      - blinker==1.8.2
      - cachetools==5.5.2
      - cffi==1.17.1
      - click==8.1.8
      - colorama==0.4.6
      - comm==0.2.3
      - configargparse==1.7.1
      - contourpy==1.1.1
      - crcmod==1.7
      - cryptography==45.0.6
      - cycler==0.12.1
      - dash==3.2.0
      - decorator==5.2.1
      - descartes==1.1.0
      - exceptiongroup==1.3.0
      - executing==2.2.0
      - fastapi==0.119.1
      - fastjsonschema==2.21.2
      - filelock==3.14.0
      - fire==0.7.1
      - flake8==7.1.2
      - flask==3.0.3
      - fonttools==4.57.0
      - google-auth==2.40.3
      - google-auth-oauthlib==1.0.0
      - grpcio==1.70.0
      - h11==0.16.0
      - httpcore==1.0.9
      - httpx==0.28.1
      - imageio==2.35.1
      - importlib-metadata==8.5.0
      - importlib-resources==6.4.5
      - iniconfig==2.1.0
      - ipython==8.12.3
      - ipywidgets==8.1.7
      - itsdangerous==2.2.0
      - jedi==0.19.2
      - jmespath==0.10.0
      - joblib==1.4.2
      - jsonschema==4.23.0
      - jsonschema-specifications==2023.12.1
      - jupyter-core==5.8.1
      - jupyterlab-widgets==3.0.15
      - kiwisolver==1.4.7
      - lazy-loader==0.4
      - llvmlite==0.41.1
      - lyft-dataset-sdk==0.0.8
      - markdown==3.7
      - markdown-it-py==3.0.0
      - matplotlib==3.5.3
      - matplotlib-inline==0.1.7
      - mccabe==0.7.0
      - mdurl==0.1.2
      - mmcv==2.1.0
      - mmdet==3.3.0
      - mmdet3d==1.4.0
      - mmengine==0.10.7
      - model-index==0.1.11
      - mypy-extensions==1.1.0
      - narwhals==1.42.1
      - nbformat==5.10.4
      - numba==0.58.1
      - nuscenes-devkit==1.1.11
      - oauthlib==3.3.1
      - open3d==0.19.0
      - opencv-python==*********
      - opendatalab==0.0.10
      - openmim==0.3.9
      - openxlab==0.1.2
      - ordered-set==4.1.0
      - oss2==2.17.0
      - packaging==24.2
      - pandas==2.0.3
      - parso==0.8.5
      - pathspec==0.12.1
      - pexpect==4.9.0
      - pkgutil-resolve-name==1.3.10
      - platformdirs==4.3.6
      - plotly==6.3.0
      - pluggy==1.5.0
      - plyfile==1.0.3
      - prompt-toolkit==3.0.52
      - protobuf==5.29.5
      - pure-eval==0.2.3
      - pyasn1==0.6.1
      - pyasn1-modules==0.4.2
      - pycocotools==2.0.7
      - pycodestyle==2.12.1
      - pycparser==2.22
      - pycryptodome==3.23.0
      - pydantic==2.10.6
      - pydantic-core==2.27.2
      - pyflakes==3.2.0
      - pygments==2.19.2
      - pyparsing==3.1.4
      - pyquaternion==0.9.9
      - pytest==8.3.5
      - pytz==2023.4
      - pywavelets==1.4.1
      - pyyaml==6.0.2
      - referencing==0.35.1
      - requests==2.28.2
      - requests-oauthlib==2.0.0
      - retrying==1.4.2
      - rich==13.4.2
      - rpds-py==0.20.1
      - rsa==4.9.1
      - scikit-image==0.21.0
      - scikit-learn==1.3.2
      - scipy==1.10.1
      - setuptools==60.2.0
      - shapely==1.8.5.post1
      - six==1.17.0
      - sniffio==1.3.1
      - stack-data==0.6.3
      - starlette==0.44.0
      - tabulate==0.9.0
      - tensorboard==2.14.0
      - tensorboard-data-server==0.7.2
      - termcolor==2.4.0
      - terminaltables==3.1.10
      - threadpoolctl==3.5.0
      - tifffile==2023.7.10
      - tomli==2.2.1
      - tqdm==4.65.2
      - trimesh==4.7.4
      - typing-extensions==4.13.2
      - tzdata==2025.2
      - urllib3==1.26.20
      - uvicorn==0.33.0
      - wcwidth==0.2.13
      - werkzeug==3.0.6
      - widgetsnbextension==4.0.14
      - yapf==0.43.0
prefix: /root/miniconda3/envs/openmmlab
