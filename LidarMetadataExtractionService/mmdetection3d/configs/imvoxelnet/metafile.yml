Collections:
  - Name: ImVoxelNet
    Metadata:
      Training Data: KITTI
      Training Techniques:
        - AdamW
      Training Resources: 8x Tesla P40
      Architecture:
        - Anchor3DHead
    Paper:
      URL: https://arxiv.org/abs/2106.01178
      Title: 'ImVoxelNet: Image to Voxels Projection for Monocular and Multi-View General-Purpose 3D Object Detection'
    README: configs/imvoxelnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection3d/blob/master/mmdet3d/models/detectors/imvoxelnet.py#L11
      Version: v0.15.0

Models:
  - Name: imvoxelnet_kitti-3d-car
    In Collection: ImVoxelNet
    Config: configs/imvoxelnet/imvoxelnet_8xb4_kitti-3d-car.py
    Metadata:
      Training Memory (GB): 15.0
    Results:
      - Task: 3D Object Detection
        Dataset: KITTI
        Metrics:
          mAP: 17.26
    Weights: https://download.openmmlab.com/mmdetection3d/v1.0.0_models/imvoxelnet/imvoxelnet_4x8_kitti-3d-car/imvoxelnet_4x8_kitti-3d-car_20210830_003014-3d0ffdf4.pth
