Collections:
  - Name: DGCN<PERSON>
    Metadata:
      Training Techniques:
        - SGD
      Training Resources: 4x Titan XP GPUs
      Architecture:
        - DGCNN
    Paper: https://arxiv.org/abs/1801.07829
    README: configs/dgcnn/README.md

Models:
  - Name: dgcnn_4xb32-cosine-100e_s3dis-seg_test-area1.py
    In Collection: DGCNN
    Config: configs/dgcnn/dgcnn_4xb32-cosine-100e_s3dis-seg_test-area1.py
    Metadata:
      Training Data: S3DIS
      Training Memory (GB): 13.3
    Results:
      - Task: 3D Semantic Segmentation
        Dataset: S3DIS Area1
        Metrics:
          mIoU: 68.33
    Weights: https://download.openmmlab.com/mmdetection3d/v0.17.0_models/dgcnn/dgcnn_32x4_cosine_100e_s3dis_seg-3d-13class/area1/dgcnn_32x4_cosine_100e_s3dis_seg-3d-13class_20210731_000734-39658f14.pth

  - Name: dgcnn_4xb32-cosine-100e_s3dis-seg_test-area2.py
    In Collection: DGCNN
    Config: configs/dgcnn/dgcnn_4xb32-cosine-100e_s3dis-seg_test-area2.py
    Metadata:
      Training Data: S3DIS
      Training Memory (GB): 13.3
    Results:
      - Task: 3D Semantic Segmentation
        Dataset: S3DIS Area2
        Metrics:
          mIoU: 40.68
    Weights: https://download.openmmlab.com/mmdetection3d/v0.17.0_models/dgcnn/dgcnn_32x4_cosine_100e_s3dis_seg-3d-13class/area2/dgcnn_32x4_cosine_100e_s3dis_seg-3d-13class_20210731_144648-aea9ecb6.pth

  - Name: dgcnn_4xb32-cosine-100e_s3dis-seg_test-area3.py
    In Collection: DGCNN
    Config: configs/dgcnn/dgcnn_4xb32-cosine-100e_s3dis-seg_test-area3.py
    Metadata:
      Training Data: S3DIS
      Training Memory (GB): 13.3
    Results:
      - Task: 3D Semantic Segmentation
        Dataset: S3DIS Area3
        Metrics:
          mIoU: 69.38
    Weights: https://download.openmmlab.com/mmdetection3d/v0.17.0_models/dgcnn/dgcnn_32x4_cosine_100e_s3dis_seg-3d-13class/area3/dgcnn_32x4_cosine_100e_s3dis_seg-3d-13class_20210801_154629-2ff50ee0.pth

  - Name: dgcnn_4xb32-cosine-100e_s3dis-seg_test-area4.py
    In Collection: DGCNN
    Config: configs/dgcnn/dgcnn_4xb32-cosine-100e_s3dis-seg_test-area4.py
    Metadata:
      Training Data: S3DIS
      Training Memory (GB): 13.3
    Results:
      - Task: 3D Semantic Segmentation
        Dataset: S3DIS Area4
        Metrics:
          mIoU: 50.07
    Weights: https://download.openmmlab.com/mmdetection3d/v0.17.0_models/dgcnn/dgcnn_32x4_cosine_100e_s3dis_seg-3d-13class/area4/dgcnn_32x4_cosine_100e_s3dis_seg-3d-13class_20210802_073551-dffab9cd.pth

  - Name: dgcnn_4xb32-cosine-100e_s3dis-seg_test-area5.py
    In Collection: DGCNN
    Config: configs/dgcnn/dgcnn_4xb32-cosine-100e_s3dis-seg_test-area5.py
    Metadata:
      Training Data: S3DIS
      Training Memory (GB): 13.3
    Results:
      - Task: 3D Semantic Segmentation
        Dataset: S3DIS Area5
        Metrics:
          mIoU: 50.59
    Weights: https://download.openmmlab.com/mmdetection3d/v0.17.0_models/dgcnn/dgcnn_32x4_cosine_100e_s3dis_seg-3d-13class/area5/dgcnn_32x4_cosine_100e_s3dis_seg-3d-13class_20210730_235824-f277e0c5.pth

  - Name: dgcnn_4xb32-cosine-100e_s3dis-seg_test-area6.py
    In Collection: DGCNN
    Config: configs/dgcnn/dgcnn_4xb32-cosine-100e_s3dis-seg_test-area6.py
    Metadata:
      Training Data: S3DIS
      Training Memory (GB): 13.3
    Results:
      - Task: 3D Semantic Segmentation
        Dataset: S3DIS Area6
        Metrics:
          mIoU: 77.94
    Weights: https://download.openmmlab.com/mmdetection3d/v0.17.0_models/dgcnn/dgcnn_32x4_cosine_100e_s3dis_seg-3d-13class/area6/dgcnn_32x4_cosine_100e_s3dis_seg-3d-13class_20210802_154317-e3511b32.pth
