import os
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import base64
import numpy as np
import torch
from mmdet3d.apis import init_model, inference_detector
import httpx
from datetime import datetime, timedelta

app = FastAPI(title="LiDAR Detection API")

api_url = os.getenv("BACKEND_API_URL", "http://backend:8000/api/v1/update_lidar")

class LiDARPayload(BaseModel):
    timestamp: str
    num_points: int
    points_shape: list
    points_data: str

class LidarMetadata(BaseModel):
    timestamp: str
    context: str

def parse_mmdet3d_data(data):
    """Parse MMDetection3D data dict into human-readable info."""
    
    # --- 1. Inputs (raw point cloud) ---
    pc = data["inputs"]["points"].cpu().numpy()
    num_points = pc.shape[0]
    print("📌 Point Cloud Info")
    print(f"  Source file     : {data['data_samples'].metainfo['lidar_path']}")
    print(f"  Num. of points  : {num_points}")
    print(f"  First 5 points  :\n{pc[:5]}\n")
    
    # --- 2. Metadata ---
    meta = data["data_samples"].metainfo
    print("📌 Meta Information")
    for k, v in meta.items():
        if isinstance(v, (np.ndarray, list, tuple)):
            print(f"  {k}: shape={np.array(v).shape}")
        else:
            print(f"  {k}: {v}")
    print()
    
    # --- 3. Ground Truth Instances 3D ---
    gt3d = getattr(data["data_samples"], "gt_instances_3d", None)
    if gt3d is not None and hasattr(gt3d, "bboxes_3d"):
        gt_bboxes = gt3d.bboxes_3d.tensor.cpu().numpy()
        gt_labels = gt3d.labels_3d.cpu().numpy() if hasattr(gt3d, "labels_3d") else []
        print("📌 Ground Truth 3D Boxes")
        if len(gt_bboxes) > 0:
            for i, box in enumerate(gt_bboxes):
                label = gt_labels[i] if len(gt_labels) > i else "?"
                print(f"  Box {i}: {box.tolist()} | Label={label}")
        else:
            print("  No GT 3D boxes found.")
    else:
        print("📌 Ground Truth 3D Boxes: Not available")
    print()
    
    # --- 4. Ground Truth Point Segmentation ---
    gt_pts_seg = getattr(data["data_samples"], "gt_pts_seg", None)
    if gt_pts_seg is not None and hasattr(gt_pts_seg, "pts_semantic_mask"):
        seg_mask = gt_pts_seg.pts_semantic_mask.cpu().numpy()
        print("📌 Point Segmentation Labels")
        print(f"  Num. labels: {len(np.unique(seg_mask))}")
        print(f"  Unique labels: {np.unique(seg_mask)}")
    else:
        print("📌 Point Segmentation Labels: Not available")
    print()
    
    # --- 5. Ground Truth 2D Instances ---
    gt_instances = getattr(data["data_samples"], "gt_instances", None)
    if gt_instances is not None and hasattr(gt_instances, "bboxes"):
        print("📌 Ground Truth 2D Instances (image space)")
        print(gt_instances)
    else:
        print("📌 Ground Truth 2D Instances: Not available")
    print()



def parse_mmdet3d_result(result, dataset="kitti", conf_thresh=0.5):
    """
    Parse MMDetection3D inference result (Det3DDataSample) into a readable string.
    """

    info_lines = []

    # Define class maps for different datasets
    if dataset == "kitti":
        class_map = {0: "Car", 1: "Pedestrian", 2: "Cyclist"}
    elif dataset == "nuscenes":
        class_map = {
            0: "Car", 1: "Pedestrian", 2: "Truck", 3: "Bus", 4: "Trailer",
            5: "Construction Vehicle", 6: "Bicycle", 7: "Motorcycle",
            8: "Pedestrian Group", 9: "Other Vehicle"
        }
    else:
        raise ValueError(f"Unsupported dataset: {dataset}")

    # --- 1. Metadata ---
    meta = result.metainfo
    info_lines.append("Meta Information:")
    for k, v in meta.items():
        if isinstance(v, (np.ndarray, list, tuple)):
            info_lines.append(f"  {k}: shape={np.array(v).shape}")
        else:
            info_lines.append(f"  {k}: {v}")
    info_lines.append("")

    # --- 2. Predicted 3D Instances ---
    pred = getattr(result, "pred_instances_3d", None)
    if pred is not None and hasattr(pred, "bboxes_3d"):
        bboxes = pred.bboxes_3d.tensor.cpu().numpy()
        scores = pred.scores_3d.cpu().numpy()
        labels = pred.labels_3d.cpu().numpy()

        class_names = [class_map.get(l, str(l)) for l in labels]

        info_lines.append(f"Predicted 3D Boxes (score >= {conf_thresh})")
        displayed = 0
        for i in range(len(bboxes)):
            score = scores[i]
            if score < conf_thresh:
                continue
            box = bboxes[i]
            label = class_names[i]
            info_lines.append(f"  Detection {i}:")
            info_lines.append(f"    Class : {label}")
            info_lines.append(f"    Score : {score:.3f}")
            info_lines.append(
                f"    BBox  : x={box[0]:.2f}, y={box[1]:.2f}, z={box[2]:.2f}, "
                f"dx={box[3]:.2f}, dy={box[4]:.2f}, dz={box[5]:.2f}, yaw={box[6]:.2f}"
            )
            displayed += 1
        if displayed == 0:
            info_lines.append("  None above confidence threshold.")
        info_lines.append("")
    else:
        info_lines.append("📌 Predicted 3D Boxes: None\n")

    # --- 3. Ground Truth Instances (if available) ---
    gt3d = getattr(result, "gt_instances_3d", None)
    if gt3d is not None and hasattr(gt3d, "bboxes_3d"):
        gt_bboxes = gt3d.bboxes_3d.tensor.cpu().numpy()
        info_lines.append("📌 Ground Truth 3D Boxes:")
        info_lines.append(str(gt_bboxes))
    else:
        info_lines.append("📌 Ground Truth 3D Boxes: Not available")
    info_lines.append("")

    return "\n".join(info_lines)

print("Loading MMDetection3D model...")
config_file = 'configs/pointpillars/pointpillars_hv_secfpn_sbn-all_8xb2-amp-2x_nus-3d.py'
checkpoint_file = "checkpoints/hv_pointpillars_fpn_sbn-all_fp16_2x8_2x_nus-3d_20201021_120719-269f9dd6.pth"
device = 'cuda:0' if torch.cuda.is_available() else 'cpu'
model = init_model(config_file, checkpoint_file, device=device)
print("MMDetection3D model loaded on", device)


@app.get("/")
async def root():
    return {
        "message": "Welcome to Metadata Extraction Service!",
        "docs_url": "http://localhost:8081/docs"
        }

@app.get("/health")
async def health_check():
    return {"status": "healthy", "app_name": config.app_name}

@app.post("/infer")
async def infer_lidar(payload: LiDARPayload):
    try:
        # Decode Base64 → float32 array
        points_bytes = base64.b64decode(payload.points_data)
        expected_size = np.prod(payload.points_shape)
        if len(points_bytes) != expected_size * 4:
            raise HTTPException(status_code=400,
                                detail=f"Data size mismatch: buffer has {len(points_bytes)} bytes, expected {expected_size*4}")
        points = np.frombuffer(points_bytes, dtype=np.float32).reshape(payload.points_shape)

        # Optional: save to temp file
        tmp_pcd_path = "/tmp/lidar_frame.bin"
        points.tofile(tmp_pcd_path)

        # Run inference
        result, data = inference_detector(model, tmp_pcd_path)
        context_str = parse_mmdet3d_result(result,"nuscenes",0.65)
        print(f"[INFO] Timestamp: {payload.timestamp}")
        print(f"[INFO] Number of points: {payload.num_points}")
        print(f"[INFO] Context: {context_str}")
        print(f"[INFO] Sending metadata to backend...")
        # --- Format timestamp into readable format ---
        try:
            if "_" in payload.timestamp:
                seconds_part, nano_part = payload.timestamp.split("_")
                seconds = int(seconds_part)
                microseconds = int(nano_part[:6].ljust(6, "0"))  # take first 6 digits for microseconds
                dt = datetime.fromtimestamp(seconds).replace(microsecond=microseconds)
                readable_time = dt.strftime("%Y-%m-%d %H:%M:%S.%f")  # includes microseconds
            elif payload.timestamp.isdigit():
                dt = datetime.fromtimestamp(int(payload.timestamp))
                readable_time = dt.strftime("%Y-%m-%d %H:%M:%S")
            else:
                dt = datetime.fromisoformat(payload.timestamp.replace("Z", "+00:00"))
                readable_time = dt.strftime("%Y-%m-%d %H:%M:%S.%f")
        except Exception:
            readable_time = payload.timestamp

        # Create metadata object
        metadata = LidarMetadata(
            timestamp=readable_time,
            context=context_str
        )
        # --- Send metadata to backend ---
        async with httpx.AsyncClient(timeout=10) as client:
            response = await client.post(api_url, json=metadata.dict())

        if response.status_code != 200:
            raise HTTPException(
                status_code=response.status_code,
                detail=f"Failed to send metadata to backend: {response.text}"
            )

        return {
            "status": "sent_to_backend",
            "backend_response": response.json()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app:app", host="0.0.0.0", port=8081, reload=True)
