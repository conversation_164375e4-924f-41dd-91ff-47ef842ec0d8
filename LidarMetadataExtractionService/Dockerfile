# Base image
FROM eusmartedge/mmdetection3d:v1

# Avoid interactive prompts
ENV DEBIAN_FRONTEND=noninteractive

# Miniconda install location
ENV CONDA_DIR=/opt/conda
ENV PATH=$CONDA_DIR/bin:$PATH

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    bzip2 \
    ca-certificates \
    git \
    build-essential \
    libgl1-mesa-glx \
    && rm -rf /var/lib/apt/lists/*

# Install Miniconda if not already installed
RUN if ! command -v conda &> /dev/null; then \
        echo "Installing Miniconda..."; \
        wget --quiet https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O /tmp/miniconda.sh && \
        bash /tmp/miniconda.sh -b -p $CONDA_DIR && \
        rm /tmp/miniconda.sh; \
    else \
        echo "Conda already installed, skipping"; \
    fi

# Set working directory
WORKDIR /mmdetection3d

# ✅ Copy ONLY the environment file first (for layer caching)
COPY ./mmdetection3d/openmmlab.yaml ./openmmlab.yaml

# ✅ Create the conda environment (cached unless YAML changes)
RUN conda update -n base -c defaults conda -y && \
    conda env create -f openmmlab.yaml

# Use the new conda environment
SHELL ["conda", "run", "-n", "openmmlab", "/bin/bash", "-c"]

# ✅ Now copy the rest of your project files (frequently changing)
COPY ./mmdetection3d/ .

# Expose FastAPI port
EXPOSE 8081

# Default command to start FastAPI app
CMD ["conda", "run", "--no-capture-output", "-n", "openmmlab", "python", "-m", "uvicorn", "app:app", "--host", "0.0.0.0", "--port", "8081", "--reload"]
