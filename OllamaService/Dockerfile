# ============================================================
# Use official Ollama base image with GPU support
# ============================================================
FROM ollama/ollama:latest

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Expose Ollama API port
EXPOSE 11434

# Copy the preload script
COPY preload-model.sh /usr/local/bin/preload-model.sh
RUN chmod +x /usr/local/bin/preload-model.sh

# Override the entrypoint and start Ollama with preload model
ENTRYPOINT []
CMD ["/usr/local/bin/preload-model.sh"]
