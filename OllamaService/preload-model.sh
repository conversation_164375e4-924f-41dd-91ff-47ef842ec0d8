#!/bin/bash
set -e

# Start Ollama in background
ollama serve &
OLLAMA_PID=$!

# Wait for Ollama API to start up
echo "Waiting for Ollama to start..."
until curl -s http://localhost:11434/api/tags > /dev/null; do
  sleep 2
done

# Preload model
echo "Pulling model llama3:8b ..."
ollama pull llama3:8b
echo "Pulled successfully!"

# Preload model
echo "Pulling model gemma3:4b ..."
ollama pull gemma3:4b
echo "Pulled successfully!"

# Keep Ollama running in foreground
wait $OLLAMA_PID