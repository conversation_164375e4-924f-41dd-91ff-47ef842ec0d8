#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import PointCloud2
import sensor_msgs_py.point_cloud2 as pc2
import numpy as np
import requests
from datetime import datetime
from pathlib import Path
import json
import base64


class LidarExtractor(Node):
    def __init__(self):
        super().__init__('lidar_extractor')

        # LiDAR subscription
        self.lidar_subscription = self.create_subscription(
            PointCloud2,
            '/rslidar_points',
            self.process_pointcloud,
            10
        )

        # Endpoint of the FastAPI microservice
        self.api_url = "http://localhost:8000/infer"

        self.get_logger().info("LidarExtractor node started!")
        self.get_logger().info(f"Sending LiDAR data to {self.api_url}")

    def process_pointcloud(self, msg):
        """Convert PointCloud2 to numpy array, send to FastAPI."""
        # Convert ROS PointCloud2 → NumPy array
        points = np.array(list(pc2.read_points(msg, field_names=("x", "y", "z"), skip_nans=True)))
        num_points = points.shape[0]

        if num_points == 0:
            self.get_logger().warn("Received empty point cloud — skipping inference.")
            return

        timestamp = f"{msg.header.stamp.sec}_{msg.header.stamp.nanosec}"
        self.get_logger().info(f"Processing LiDAR frame: {timestamp} with {num_points} points")

        # Serialize points (float32) → Base64 string
        points_bytes = points.astype(np.float32).tobytes()
        encoded_points = base64.b64encode(points_bytes).decode('utf-8')

        # Prepare payload
        payload = {
            "timestamp": timestamp,
            "num_points": num_points,
            "points_shape": points.shape,
            "points_data": encoded_points
        }

        try:
            response = requests.post(self.api_url, json=payload, timeout=10)
            if response.status_code == 200:
                result = response.json()
                self.get_logger().info(f"Inference result: {result.get('status')} — {result.get('message')}")
            else:
                self.get_logger().error(f"Server error {response.status_code}: {response.text}")
        except requests.exceptions.RequestException as e:
            self.get_logger().error(f"Request failed: {e}")


def main(args=None):
    rclpy.init(args=args)
    node = LidarExtractor()
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
