# Use ROS Humble as base
FROM ros:humble

# Set working directory
WORKDIR /ros_service

# Avoid interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# ------------------------------
# Install system dependencies
# ------------------------------
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        python3-pip \
        locales \
        software-properties-common \
        curl \
        gnupg2 \
        lsb-release \
        wget \
        build-essential && \
    locale-gen en_US.UTF-8 && \
    update-locale LANG=en_US.UTF-8 LC_ALL=en_US.UTF-8 && \
    rm -rf /var/lib/apt/lists/*

# ------------------------------
# Copy and install Python dependencies
# ------------------------------
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade -r requirements.txt

# ------------------------------
# Install ROS packages if not already installed
# ------------------------------
RUN if ! dpkg -s ros-humble-desktop >/dev/null 2>&1; then \
        apt-get update && \
        apt-get install -y --no-install-recommends \
            ros-humble-desktop \
            ros-humble-ros-base \
            ros-dev-tools \
            ros-humble-rosbag2-storage-mcap && \
        rm -rf /var/lib/apt/lists/*; \
    fi

# ------------------------------
# Copy application files
# ------------------------------
COPY main_local.py .
COPY start-dev.sh .

# ------------------------------
# Make startup script executable
# ------------------------------
RUN chmod +x start-dev.sh

# ------------------------------
# Source ROS setup automatically
# ------------------------------
SHELL ["/bin/bash", "-c"]
RUN echo "source /opt/ros/humble/setup.bash" >> ~/.bashrc

# ------------------------------
# Default command
# ------------------------------
CMD ["./start-dev.sh"]
