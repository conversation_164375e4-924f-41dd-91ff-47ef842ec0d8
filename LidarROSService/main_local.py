#!/usr/bin/env python3

import os
import time
import rclpy
from rclpy.node import Node
from rclpy.qos import qos_profile_sensor_data
from sensor_msgs.msg import PointCloud2
import sensor_msgs_py.point_cloud2 as pc2
import numpy as np
import requests
import base64
import traceback


class LidarExtractor(Node):
    def __init__(self):
        super().__init__('lidar_extractor')

        # Subscribe to LiDAR topic
        self.lidar_subscription = self.create_subscription(
            PointCloud2,
            '/rslidar_points',
            self.process_pointcloud,
            qos_profile=qos_profile_sensor_data
        )

        # FastAPI endpoint
        self.api_url = os.getenv("METADATA_API_URL", "http://mmdetection3d:8081/infer")

        self.get_logger().info(f"LidarExtractor node started! Sending data to {self.api_url}")

        # Wait for FastAPI to be ready
        self.wait_for_api()

    def wait_for_api(self, max_retries=10, delay=2):
        for i in range(max_retries):
            try:
                r = requests.get(self.api_url.replace("/infer", "/health"), timeout=2)
                if r.status_code == 200:
                    self.get_logger().info("FastAPI service is ready!")
                    return
            except requests.exceptions.RequestException:
                self.get_logger().warn(f"Waiting for FastAPI service ({i+1}/{max_retries})...")
                time.sleep(delay)
        self.get_logger().error("FastAPI service not available after waiting!")

    def send_request_with_retry(self, payload, max_retries=3, delay=2):
        for i in range(max_retries):
            try:
                response = requests.post(self.api_url, json=payload, timeout=10)
                return response
            except requests.exceptions.RequestException as e:
                self.get_logger().warn(f"Attempt {i+1}/{max_retries} failed: {e}")
                time.sleep(delay)
        return None

    def process_pointcloud(self, msg):
        self.get_logger().info(
            f"Callback triggered for frame {msg.header.stamp.sec}_{msg.header.stamp.nanosec}"
        )
        try:
            # Read points (x,y,z) and convert to plain float list
            points_iter = pc2.read_points(msg, field_names=("x","y","z"), skip_nans=True)
            points_list = [[p[0], p[1], p[2]] for p in points_iter]

            num_points = len(points_list)
            if num_points == 0:
                self.get_logger().warn("Empty point cloud — skipping inference.")
                return

            points = np.array(points_list, dtype=np.float32)
            timestamp = f"{msg.header.stamp.sec}_{msg.header.stamp.nanosec}"

            self.get_logger().info(f"Processing frame: {timestamp} with {num_points} points")

            # Serialize → Base64 (truncate for printing)
            points_bytes = points.tobytes()
            encoded_points = base64.b64encode(points_bytes).decode('utf-8')
            print(f"points_data (truncated): {encoded_points[:100]}...", flush=True)

            # Payload with correct shape
            payload = {
                "timestamp": timestamp,
                "num_points": num_points,
                "points_shape": list(points.shape),
                "points_data": encoded_points
            }

            # Send request with retries
            response = self.send_request_with_retry(payload)
            if response is None:
                self.get_logger().error("Failed to connect to FastAPI after retries")
            elif response.status_code == 200:
                result = response.json()
                self.get_logger().info(f"Inference result: {result.get('status')}")
            else:
                self.get_logger().error(f"Server error {response.status_code}: {response.text}")

        except Exception as e:
            self.get_logger().error(f"Exception in callback: {e}\n{traceback.format_exc()}")


def main(args=None):
    rclpy.init(args=args)
    node = LidarExtractor()
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
