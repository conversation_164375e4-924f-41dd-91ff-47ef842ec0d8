#!/bin/bash

# Usage: ./script.sh <container_name> <dockerfile_path>
if [ -z "$1" ] || [ -z "$2" ]; then
    echo "Usage: $0 <container_name> <dockerfile_path>"
    exit 1
fi

CONTAINER_NAME=$1
DOCKERFILE_PATH=$2

# Build Docker image from the Dockerfile
IMAGE_NAME="${CONTAINER_NAME}_image"
docker build -t $IMAGE_NAME -f $DOCKERFILE_PATH .

# Run the container
docker run --gpus all -di -m 1800g -w /root \
    -v /data/docker_workspace/$CONTAINER_NAME:/root \
    -v /brain-data:/brain-data:ro \
    -v /data:/data:ro \
    --shm-size="500g" --ulimit memlock=-1 --ulimit stack=67108864 \
    --name $CONTAINER_NAME $IMAGE_NAME

# Copy initialization script
docker cp $HOME/.init.sh $CONTAINER_NAME:/root/.init.sh
docker exec -i $CONTAINER_NAME bash -c "bash /root/.init.sh"

echo "Finish!"
echo "Access your docker by run:"
echo "ssh root@$CONTAINER_NAME"
echo "Or:"
echo "docker exec -it $CONTAINER_NAME bash"
