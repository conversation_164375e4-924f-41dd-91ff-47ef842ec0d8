#!/bin/bash
# Source ROS 2 environment
source /opt/ros/humble/setup.bash

# Path to your bag file
BAG_FILE="/data/rahul_workspace/subset_20250902_132418_fix/20250902_132418_0.mcap"
PYTHON_FILE="/ros_service/main_local.py"

# Start Python script in background
python3 "$PYTHON_FILE" &

# Continuous playback loop
while true; do
    echo "Starting ros2 bag playback..."
    ros2 bag play "$BAG_FILE" --topics /rslidar_points --clock
    echo "Playback finished, restarting..."
    sleep 1
done
