#!/usr/bin/env python3
"""
Test script to verify the model and prompt selection implementation.
This script tests the backend API endpoints to ensure they work correctly.
"""

import requests
import json
import sys

def test_backend_endpoints(base_url="http://localhost:8000"):
    """Test the backend API endpoints."""
    
    print("🧪 Testing LidarInferenceService API endpoints...")
    print(f"Base URL: {base_url}")
    print("-" * 50)
    
    # Test 1: Check if service is healthy
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Health check passed")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to backend service: {e}")
        return False
    
    # Test 2: Get available models
    try:
        response = requests.get(f"{base_url}/api/v1/available-models", timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            print(f"✅ Available models: {models}")
        else:
            print(f"❌ Failed to get available models: {response.status_code}")
    except Exception as e:
        print(f"❌ Error getting available models: {e}")
    
    # Test 3: Get available prompts
    try:
        response = requests.get(f"{base_url}/api/v1/available-prompts", timeout=5)
        if response.status_code == 200:
            prompts = response.json().get("prompts", [])
            print(f"✅ Available prompts: {prompts}")
        else:
            print(f"❌ Failed to get available prompts: {response.status_code}")
    except Exception as e:
        print(f"❌ Error getting available prompts: {e}")
    
    # Test 4: Test generate endpoint with model and prompt selection
    test_payload = {
        "prompt": "What objects do you see in the scene?",
        "context": "Test context data",
        "model_name": "llama3:8b",
        "system_prompt": "Autonomous Driving Assistant"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/generate", 
            json=test_payload, 
            timeout=30
        )
        if response.status_code == 200:
            result = response.json()
            print("✅ Generate endpoint test passed")
            print(f"Response preview: {result.get('response', '')[:100]}...")
        else:
            print(f"❌ Generate endpoint test failed: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Error testing generate endpoint: {e}")
    
    print("-" * 50)
    print("🎯 Backend API testing completed!")
    return True

def test_streamlit_imports():
    """Test if Streamlit app imports work correctly."""
    print("\n🧪 Testing Streamlit app imports...")
    print("-" * 50)
    
    try:
        # Test backend imports
        sys.path.append('LidarInferenceService/app')
        from models.llm import RequestModel
        from prompts.llm import AVAILABLE_PROMPTS
        from services.LLMService import LLMService
        
        print("✅ Backend imports successful")
        print(f"Available prompts: {list(AVAILABLE_PROMPTS.keys())}")
        
        # Test creating a request model
        request = RequestModel(
            prompt="test",
            context="test context",
            model_name="llama3:8b",
            system_prompt="Autonomous Driving Assistant"
        )
        print("✅ RequestModel creation successful")
        
    except Exception as e:
        print(f"❌ Backend import error: {e}")
    
    try:
        # Test frontend imports
        sys.path.append('StreamlitUIService')
        from prompts import templates
        
        print("✅ Frontend imports successful")
        print(f"Available prompts: {list(templates.AVAILABLE_PROMPTS.keys())}")
        
    except Exception as e:
        print(f"❌ Frontend import error: {e}")
    
    print("-" * 50)
    print("🎯 Import testing completed!")

if __name__ == "__main__":
    print("🚀 Starting implementation tests...")
    
    # Test imports first
    test_streamlit_imports()
    
    # Test backend endpoints if service is running
    if len(sys.argv) > 1 and sys.argv[1] == "--test-backend":
        test_backend_endpoints()
    else:
        print("\n💡 To test backend endpoints, run:")
        print("   python test_implementation.py --test-backend")
        print("   (Make sure the backend service is running first)")
    
    print("\n✨ All tests completed!")
